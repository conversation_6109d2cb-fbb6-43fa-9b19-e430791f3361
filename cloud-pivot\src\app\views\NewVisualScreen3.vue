<template>
  <div class="holographic-container" ref="container">
    <div class="grid-container">
      <!-- 重点人员管控 - 第1列，第1-2行 -->
      <div class="important-persons grid-item-persons">
        <div class="important-persons-title-containter">
          <div class="title-left">
            <el-icon><Avatar /></el-icon>
            <div class="important-persons-title">重点人员管控</div>
          </div>
          <div class="pagination-info">{{ currentPersonPageDisplay }}/{{ totalPersonPages }}</div>
        </div>
        <!-- 使用 el-card 展示重点人员信息 -->
        <transition-group name="person-card">
          <el-card
            v-for="(person, index) in paginatedImportantPersons"
            :key="index"
            class="person-card">
          <template #header>
            <div class="card-header">
              <span class="person-name">{{ person.name }}</span>
              <span class="person-area">{{ person.area }}</span>
              <span class="person-type">{{ person.type }}</span>
              <span class="person-status">{{ person.status }}</span>
            </div>
          </template>
          <div v-if="person.movements.length > 0">
            <div class="movement-section">
              <div class="movement-header">
                <span class="movement-title">最新动向</span>
                <span class="movement-time">动向更新时间：{{ person.movements[0].time }}</span>
              </div>
              <div class="movement-content">
                {{ person.movements[0].details }}
              </div>
            </div>
          </div>
          </el-card>
        </transition-group>

        <!-- 人员状态统计滚动展示 -->
        <div class="status-statistics-container">
          <div class="status-statistics-scroll">
            <div
              v-for="(count, status) in personStatusStatistics"
              :key="status"
              class="status-item"
            >
              <div class="status-name">{{ status }}</div>
              <div class="status-count">{{ count }}人</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 值班表 - 第1列，第3行 -->
      <div class="duty-table-container grid-item-duty">
        <div class="duty-table-title">今日值班</div>
        <table class="duty-table">
          <tr>
            <th>值班岗位</th>
            <th>A岗</th>
            <th>B岗</th>
          </tr>
          <tr v-for="(item, index) in dutyData" :key="index">
            <td>{{ item.type }}</td>
            <template v-if="index < 2">
              <td>{{ index === 0 ? item.a_dir : item.a_com }}</td>
              <td>{{ index === 0 ? item.b_dir : item.b_com }}</td>
            </template>
            <td v-else colspan="2">
              {{
                index === 2 ? item.sub :
                index === 3 ? item.op :
                index === 4 ? item.tech :
                item.sec
              }}
            </td>
          </tr>
        </table>
      </div>

      <!-- 空白容器1 - 第2列，第1-2行 -->
      <div class="blank-container grid-item-blank1">
        <!-- 空白内容 -->
      </div>

      <!-- 空白容器2 - 第2列，第3行 -->
      <div class="blank-container grid-item-blank2">
        <!-- 空白内容 -->
      </div>

      <!-- 重大案事件 - 第3列，第1-2行 -->
      <div class="important-events grid-item-events">
        <div class="important-events-title-containter">
          <div class="title-left">
            <el-icon><View /></el-icon>
            <div class="important-events-title">重大案事件跟盯</div>
          </div>
          <div class="pagination-info">{{ currentEventPageDisplay }}/{{ totalEventPages }}</div>
        </div>
        <!-- 使用 transition-group 添加动画 -->
        <transition-group name="event-card">
          <el-card
            v-for="(event, index) in paginatedImportantEvents"
            :key="index"
            class="event-card">
            <template #header>
              <div class="card-header">
                <span :class="getEventNameClass(event.level)">{{ event.name }}</span>
                <span :class="getEventAreaClass(event.level)">{{ event.area }}</span>
                <span :class="getEventStatusClass(event.level)">{{ event.status }}</span>
              </div>
            </template>
            <div class="event-details">
              <span :class="getDetailsLabelClass(event.level)">具体内容：</span>{{ event.details }}
            </div>
            <div v-if="event.actions.length > 0">
              <div :class="getDisposalSectionClass(event.level)">
                <div class="disposal-header">
                  <span :class="getDisposalTitleClass(event.level)">最新处置情况</span>
                  <span class="disposal-time">处置时间：{{ event.actions[0].time }}</span>
                </div>
                <div class="disposal-content">
                  {{ event.actions[0].measure }}
                </div>
              </div>
            </div>
          </el-card>
        </transition-group>
      </div>

      <!-- 柱状图 - 第3列，第3行 -->
      <div class="bar-chart-wrapper grid-item-chart">
        <div class="duty-table-title">今日警情分类统计</div>
        <div ref="chartContainer" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed, nextTick, watch } from 'vue';
import * as echarts from 'echarts'
import { View, Avatar, Close } from '@element-plus/icons-vue';
// 移除地图数据导入
import { DATA_CONFIG } from '../../config/dataConfig.js';

// 移除地图容器

// 移除地图相关函数

// 获取分类对应的柱状图颜色
const getCategoryColor = (category, index) => {
  // 柱状图颜色配置（与柱状图保持一致）
  const colors = [
    '#00e5ff', // 青蓝色 - 盗窃
    '#ff6b35', // 橙红色 - 诈骗（新颜色）
    '#52c41a', // 绿色 - 抢劫
    '#faad14', // 黄色 - 其他刑事
    '#fa8c16', // 橙色 - 治安案件
    '#eb2f96', // 品红色 - 交通事故
    '#722ed1', // 紫色 - 火灾
    '#13c2c2', // 青色 - 纠纷
    '#a0d911', // 青绿色 - 举报
    '#6c5ce7', // 紫色 - 群众求助（新颜色）
    '#2f54eb'  // 深蓝色 - 其他
  ];

  return colors[index % colors.length];
};

// 移除地图统计函数

// 根据警情分类获取对应的API参数
const getCategoryApiParams = (category) => {
  const categoryMap = {
    '盗窃': 'modifyAlarmCategoryCode=01000000&modifyAlarmTypeCode=01040000&modifyAlarmSubclassCode=01040300',
    '诈骗': 'modifyAlarmCategoryCode=01000000&modifyAlarmTypeCode=01040000&modifyAlarmSubclassCode=01040400', // 接触性诈骗
    '抢劫': 'modifyAlarmCategoryCode=01000000&modifyAlarmTypeCode=01040000&modifyAlarmSubclassCode=01040100',
    '其他刑事': 'modifyAlarmCategoryCode=01000000&modifyAlarmTypeCode=01990000',
    '治安案件': 'modifyAlarmCategoryCode=02000000',
    '交通事故': 'modifyAlarmCategoryCode=03000000',
    '火灾': 'modifyAlarmCategoryCode=06000000&modifyAlarmTypeCode=06080000&modifyAlarmSubclassCode=06080100',
    '纠纷': 'modifyAlarmCategoryCode=08000000',
    '举报': 'modifyAlarmCategoryCode=09000000',
    '群众求助': 'modifyAlarmCategoryCode=06000000',
    '其他': 'modifyAlarmCategoryCode=12000000'
  };

  return categoryMap[category] || 'modifyAlarmCategoryCode=12000000';
};



// 根据警情数量获取颜色配置
const getColorByCount = (count) => {
  if (count === 0){
    return {
      markerColor: 'green',    // 绿色 - 低警情
      labelColor: '#fff',        // 白色文字
      nameColor: '#fff'       // 绿色名称
    }
  }
  else if (count > 0 && count <= 5) {
    return {
      markerColor: '#0066cc',    // 绿色 - 低警情
      labelColor: '#fff',        // 白色文字
      nameColor: '#fff'      // 绿色名称
    }
  } else if (count > 5 && count <= 10) {
    return {
      markerColor: 'yellow',    // 蓝色 - 中低警情
      labelColor: '#000',        // 白色文字
      nameColor: '#fff'       // 蓝色名称
    }
  } else if (count > 10 && count <= 15) {
    return {
      markerColor: 'orange',    // 黄色 - 中等警情
      labelColor: '#fff',        // 白色文字
      nameColor: '#fff'        // 黄色名称
    }
  } else { // count > 15
    return {
      markerColor: '#ff4d4f',    // 红色 - 高警情
      labelColor: '#fff',        // 白色文字
      nameColor: '#fff'        // 红色名称
    }
  }
};

// 各辖区代码，经纬度数据
// 移除地图相关数据

// 获取当天的开始和结束时间
const getTodayTimeRange = () => {
  const today = new Date()
  const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

  const formatTime = (date) => {
    return date.getFullYear() + '-' +
           String(date.getMonth() + 1).padStart(2, '0') + '-' +
           String(date.getDate()).padStart(2, '0') + ' ' +
           String(date.getHours()).padStart(2, '0') + ':' +
           String(date.getMinutes()).padStart(2, '0') + ':' +
           String(date.getSeconds()).padStart(2, '0')
  }

  return {
    startTime: formatTime(startTime),
    endTime: formatTime(endTime)
  }
}

// Mock数据生成函数
const generateMockPoliceData = () => {
  const [min, max] = DATA_CONFIG.MOCK_CONFIG.POLICE_COUNT_RANGE;
  const range = max - min;

  // 为每个派出所生成随机警情数量（与areaData数组完全匹配）
  const mockData = {
    "511621210000": Math.floor(Math.random() * range) + min, // 花园派出所
    "511621220000": Math.floor(Math.random() * range) + min, // 顾县派出所
    "511621230000": Math.floor(Math.random() * range) + min, // 苟角派出所
    "511621270000": Math.floor(Math.random() * range) + min, // 罗渡派出所
    "511621890000": Math.floor(Math.random() * range) + min, // 白庙派出所
    "511621900000": Math.floor(Math.random() * range) + min, // 坪滩派出所
    "511621910000": Math.floor(Math.random() * range) + min, // 酉溪派出所
    "511621920000": Math.floor(Math.random() * range) + min, // 兴隆派出所
    "511621930000": Math.floor(Math.random() * range) + min, // 城东派出所
    "511621940000": Math.floor(Math.random() * range) + min, // 普安派出所
    "511621970000": Math.floor(Math.random() * range) + min, // 镇裕派出所
    "511621980000": Math.floor(Math.random() * range) + min, // 朝阳派出所
    "511621290000": Math.floor(Math.random() * range) + min, // 中和派出所
    "511621880000": Math.floor(Math.random() * range) + min, // 伏龙派出所
    "511621250000": Math.floor(Math.random() * range) + min, // 石垭派出所
    "511621070000": Math.floor(Math.random() * range) + min  // 九龙派出所
  }
  return mockData
}

// Mock数据生成函数 - 警情统计
const generateMockAlarmStatistics = () => {
  const [catMin, catMax] = DATA_CONFIG.MOCK_CONFIG.CATEGORY_COUNT_RANGE;
  const [validMin, validMax] = DATA_CONFIG.MOCK_CONFIG.VALID_ALARM_RANGE;
  const [invalidMin, invalidMax] = DATA_CONFIG.MOCK_CONFIG.INVALID_ALARM_RANGE;

  return {
    // 警情分类统计
    categories: [
      { name: '盗窃', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '诈骗', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '抢劫', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '其他刑事', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '治安案件', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '交通事故', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '火灾', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '纠纷', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '举报', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '群众求助', count: Math.floor(Math.random() * (catMax - catMin)) + catMin },
      { name: '其他', count: Math.floor(Math.random() * (catMax - catMin)) + catMin }
    ],
    // 有效/无效警情统计
    validity: [
      { name: '有效警情', count: Math.floor(Math.random() * (validMax - validMin)) + validMin },
      { name: '无效警情', count: Math.floor(Math.random() * (invalidMax - invalidMin)) + invalidMin }
    ]
  }
}

// 获取单个派出所的警情数据（支持Mock和真实接口切换）
// 移除地图相关函数

// 获取所有派出所的警情数据
// 移除地图相关的数据获取函数

// 移除地图显示函数

const importantPersons = ref([]);

// 人员状态统计数据
const personStatusStatistics = ref({
  "在南充市": 2,
  "在省": 1,
  "在广安": 1,
  "在岳池": 3
});

// 检测状态统计是否需要滚动
const checkScrollNeed = () => {
  nextTick(() => {
    const container = document.querySelector('.status-statistics-container');
    const scroll = document.querySelector('.status-statistics-scroll');

    if (container && scroll) {
      const containerWidth = container.offsetWidth;
      const scrollWidth = scroll.scrollWidth;

      // 如果内容宽度超过容器宽度，添加滚动动画
      if (scrollWidth > containerWidth) {
        scroll.classList.add('scrolling');
      } else {
        scroll.classList.remove('scrolling');
      }
    }
  });
};

  // 根据人员状态返回标签类型
  const getPersonStatusTagType = (status) => { 
    switch (status) { 
      case '在京': 
        return 'danger'; 
      case '已控制': 
        return 'success'; 
      case '观察中': 
        return 'warning'; 
      default: 
        return 'default'; 
    } 
  }; 

const importantEvents = ref([]);

  // 根据处置状态返回标签类型
  const getTagType = (status) => { 
    switch (status) { 
      case '已完成': 
        return 'success'; 
      case '处理中': 
        return 'info'; 
      case '待处理': 
        return 'warning'; 
      default: 
        return 'default'; 
    } 
  }; 
  const getLevelTagType = (level) => {
    switch (level) {
      case '严重':
        return 'danger';
      case '中':
        return 'warning';
      case '低':
        return 'success';
      default:
        return 'default';
    }
  };

// 根据level获取颜色
const getLevelColor = (level) => {
  switch (level) {
    case 1:
      return 'red';
    case 2:
      return 'orange';
    case 3:
      return 'yellow';
    default:
      return '#c41e3a'; // 默认紫红色
  }
};

// 根据level获取圆点样式类
const getLevelClass = (level) => {
  switch (level) {
    case 1:
      return 'level-red';
    case 2:
      return 'level-orange';
    case 3:
      return 'level-yellow';
    default:
      return 'level-default';
  }
};

// 根据等级获取事件名称样式类
const getEventNameClass = (level) => {
  switch (level) {
    case 1:
      return 'event-name-critical'; // 最高等级
    case 2:
      return 'event-name-high';     // 高等级
    case 3:
      return 'event-name-medium';   // 中等级
    default:
      return 'event-name-normal';   // 普通等级
  }
};

// 根据等级获取事件状态样式类
const getEventStatusClass = (level) => {
  switch (level) {
    case 1:
      return 'event-status-critical'; // 最高等级
    case 2:
      return 'event-status-high';     // 高等级
    case 3:
      return 'event-status-medium';   // 中等级
    default:
      return 'event-status-normal';   // 普通等级
  }
};

// 根据等级获取事件区域样式类
const getEventAreaClass = (level) => {
  switch (level) {
    case 1:
      return 'event-area-critical'; // 最高等级
    case 2:
      return 'event-area-high';     // 高等级
    case 3:
      return 'event-area-medium';   // 中等级
    default:
      return 'event-area-normal';   // 普通等级
  }
};

// 根据等级获取具体内容标签样式类
const getDetailsLabelClass = (level) => {
  switch (level) {
    case 1:
      return 'details-label-critical'; // 最高等级
    case 2:
      return 'details-label-high';     // 高等级
    case 3:
      return 'details-label-medium';   // 中等级
    default:
      return 'details-label-normal';   // 普通等级
  }
};

// 根据等级获取处置标题样式类
const getDisposalTitleClass = (level) => {
  switch (level) {
    case 1:
      return 'disposal-title-critical'; // 最高等级
    case 2:
      return 'disposal-title-high';     // 高等级
    case 3:
      return 'disposal-title-medium';   // 中等级
    default:
      return 'disposal-title-normal';   // 普通等级
  }
};

// 根据等级获取处置区域样式类
const getDisposalSectionClass = (level) => {
  switch (level) {
    case 1:
      return 'disposal-section disposal-section-critical'; // 最高等级
    case 2:
      return 'disposal-section disposal-section-high';     // 高等级
    case 3:
      return 'disposal-section disposal-section-medium';   // 中等级
    default:
      return 'disposal-section disposal-section-normal';   // 普通等级
  }
};

const currentPage = ref(0); // 当前页码
const dynamicPages = ref([]); // 动态分页数据，每页包含不同数量的人员
let paginationTimer = null; // 分页定时器

const importantEventsCurrentPage = ref(0);
const importantEventsPageSize = 1; // 重点事件每页显示数量
let importantEventsPaginationTimer = null;

const paginatedImportantPersons = computed(() => {
  if (dynamicPages.value.length > 0 && currentPage.value < dynamicPages.value.length) {
    return dynamicPages.value[currentPage.value];
  }
  return [];
});
const paginatedImportantEvents = computed(() => {
  const start = importantEventsCurrentPage.value * importantEventsPageSize;
  return importantEvents.value.slice(start, start + importantEventsPageSize);
});

// 计算事件页码显示
const currentEventPageDisplay = computed(() => {
  return importantEvents.value.length > 0 ? importantEventsCurrentPage.value + 1 : 0;
});

const totalEventPages = computed(() => {
  return Math.ceil(importantEvents.value.length / importantEventsPageSize);
});

// 计算人员页码显示
const currentPersonPageDisplay = computed(() => {
  return importantPersons.value.length > 0 ? currentPage.value + 1 : 0;
});

const totalPersonPages = computed(() => {
  return dynamicPages.value.length;
});

// 监听人员数据变化，重新计算页面大小
watch(importantPersons, () => {
  setTimeout(calculateDynamicPages, 500);
}, { deep: true });

// 动态计算分页数据
const calculateDynamicPages = () => {
  nextTick(() => {
    const container = document.querySelector('.important-persons');
    const titleContainer = document.querySelector('.important-persons-title-containter');

    if (container && titleContainer && importantPersons.value.length > 0) {
      // 获取容器总高度
      const containerHeight = container.clientHeight;
      // 获取标题高度
      const titleHeight = titleContainer.offsetHeight;
      // 计算可用于卡片的高度
      const availableHeight = containerHeight - titleHeight - 40;

      console.log('动态分页计算:', {
        containerHeight,
        titleHeight,
        availableHeight,
        totalPersons: importantPersons.value.length
      });

      // 保守估算：使用较大的卡片高度确保不溢出
      const avgCardHeight = 220; // 保守估算的平均卡片高度（包含margin和动向内容）
      const maxCardsPerPage = Math.max(1, Math.floor(availableHeight / avgCardHeight));

      console.log('每页最大卡片数:', maxCardsPerPage);

      // 按固定数量分页，但允许最后一页少一些
      const pages = [];
      for (let i = 0; i < importantPersons.value.length; i += maxCardsPerPage) {
        const pagePersons = importantPersons.value.slice(i, i + maxCardsPerPage);
        pages.push(pagePersons);
      }

      console.log('简化分页结果:', {
        totalPages: pages.length,
        maxCardsPerPage,
        availableHeight,
        pagesInfo: pages.map((page, index) => ({
          page: index + 1,
          count: page.length,
          persons: page.map(p => ({
            name: p.name,
            hasMovements: p.movements?.length > 0
          }))
        }))
      });

      dynamicPages.value = pages;

      // 重置当前页码，确保不超出范围
      if (currentPage.value >= pages.length) {
        currentPage.value = 0;
      }
    }
  });
};
const nextPage = () => {
  if (dynamicPages.value.length > 0) {
    currentPage.value = (currentPage.value + 1) % dynamicPages.value.length;
  }
};
const nextImportantEventsPage = () => {
  importantEventsCurrentPage.value = (importantEventsCurrentPage.value + 1) % Math.ceil(importantEvents.value.length / importantEventsPageSize);
};

let barChart = null;
let pollTimer = null; // 存储定时器ID

const chartContainer = ref(null)
// 柱状图数据将从接口获取，不再使用模拟数据

const initChart = () => {
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      backgroundColor: 'rgba(5,15,44,0.8)',
      borderColor: '#00e5ff',
      textStyle: {
        color: '#fff'
      }
    },
    xAxis: {
      type: 'category',
      data: [], // 初始为空，将通过接口数据更新
      axisLine: {
        lineStyle: {
          color: '#00e5ff'
        }
      },
      axisLabel: {
        rotate: 45,
        interval: 0,
        color: 'white',
        fontSize: 16
      }
    },
    yAxis: { 
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#00e5ff'
        }
      },
      axisLabel: {
        color: 'white',
        fontSize: 16
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0,229,255,0.2)'
        }
      }
    },
    series: [{
      type: 'bar',
      data: [], // 初始为空，将通过接口数据更新
      itemStyle: {
        color: function(params) {
          // 根据数据索引使用不同的科技蓝色渐变
          const colors = [
            ['#00e5ff', '#0066cc'], // 青蓝渐变 - 盗窃
            ['#ff6b35', '#d63031'], // 橙红渐变 - 诈骗（新颜色）
            ['#52c41a', '#237804'], // 绿蓝渐变 - 抢劫
            ['#faad14', '#d48806'], // 黄蓝渐变 - 其他刑事
            ['#fa8c16', '#d46b08'], // 橙蓝渐变 - 治安案件
            ['#f759ab', '#c41d7f'], // 粉蓝渐变 - 交通事故
            ['#722ed1', '#391085'], // 紫蓝渐变 - 火灾
            ['#13c2c2', '#006d75'], // 青色渐变 - 纠纷
            ['#a0d911', '#7cb305'], // 青绿渐变 - 举报
            ['#6c5ce7', '#5f3dc4'], // 紫色渐变 - 群众求助（新颜色）
            ['#2f54eb', '#1d39c4']  // 深蓝渐变 - 其他
          ];
          const colorPair = colors[params.dataIndex % colors.length];
          return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colorPair[0] },
            { offset: 1, color: colorPair[1] }
          ]);
        },
        borderRadius: [4, 4, 0, 0]
      },
      label: {
        show: true,
        position: 'top',
        color: '#fff',
        fontSize: 12,
        formatter: function(params) {
          return params.value;
        }
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00e5ff' },
            { offset: 1, color: '#0066cc' }
          ])
        },
        label: {
          show: true,
          color: '#00e5ff',
          fontWeight: 'bold',
          fontSize: 14
        }
      }
    }]
  }
  
  barChart.setOption(option)
  
  const handleResize = () => {
    if (barChart && !barChart.isDisposed()) {
      barChart.resize()
    }
  }
  window.addEventListener('resize', handleResize)
}

// 移除饼图相关代码


// 响应式数据
const container = ref(null);

// 全屏状态改变处理函数
const handleFullscreenChange = () => {
  setTimeout(() => {
    if (window.mapChart && !window.mapChart.isDisposed()) {
      window.mapChart.resize();
      console.log('全屏状态改变，地图已重绘');
    }
    if (barChart && !barChart.isDisposed()) {
      barChart.resize();
    }
  }, 100); // 延迟100ms确保DOM更新完成
};


let eventSource = null;

const setupSSE = () => {
  console.log('sse连接');
  eventSource = new EventSource(DATA_CONFIG.SSE_URL);

  eventSource.onmessage = function(event) {
    const responseData = JSON.parse(event.data);
    console.log('sse数据',responseData);

    // 处理值班表数据
    if (responseData.sched && responseData.sched.length > 0) {
      const schedData = responseData.sched[0];
      dutyData.value = [
        { type: '局领导', a_dir: schedData.a_dir, b_dir: schedData.b_dir },
        { type: '指挥长', a_com: schedData.a_com, b_com: schedData.b_com },
        { type: '值班长', sub: schedData.sub },
        { type: '值班员', op: Array.isArray(schedData.op) ? schedData.op.join(' ') : schedData.op },
        { type: '技术室', tech: schedData.tech },
        { type: '内部值守', sec: Array.isArray(schedData.sec) ? schedData.sec.join(' ') : schedData.sec }
      ];
    }

    // 处理重点事件数据
    if (responseData.alarm && responseData.alarm.length > 0) {
      importantEvents.value = responseData.alarm.map(alarm => ({
        id: alarm.id,
        name: alarm.alarm_name,
        details: alarm.alarm_content, // 模板中使用的是 details
        time: alarm.occur_time,
        area: alarm.area_name,
        level: alarm.alarm_level,
        status: alarm.alarm_status,
        isDisplay: alarm.is_display,
        isTop: alarm.is_top,
        actions: alarm.alarm_handling ? [{ // 模板中使用的是 actions 数组
          time: alarm.alarm_handling.handling_time,
          measure: alarm.alarm_handling.handling_content
        }] : []
      }));
    }

    // 处理重点人员数据
    if (responseData.keyperson && responseData.keyperson.length > 0) {
      importantPersons.value = responseData.keyperson.map(person => ({
        id: person.id,
        name: person.name,
        idNumber: person.id_number,
        gender: person.gender_name,
        phone: person.phone,
        type: person.type_name,
        status: person.person_movement?.status_name || '未知状态',
        area: person.unit_name,
        isDisplay: person.is_display,
        isTop: person.is_top,
        movements: person.person_movement ? [{ // 模板中使用的是 movements 数组
          time: person.person_movement.movement_time,
          details: person.person_movement.description
        }] : []
      }));

      // 使用SSE接口提供的状态统计数据
      if (responseData.statusCount && responseData.statusCount.length > 0) {
        personStatusStatistics.value = responseData.statusCount[0];
      } else {
        // 如果没有statusCount数据，则计算人员状态统计
        const statusStats = {};
        responseData.keyperson.forEach(person => {
          const statusName = person.person_movement?.status_name || '未知状态';
          statusStats[statusName] = (statusStats[statusName] || 0) + 1;
        });
        personStatusStatistics.value = statusStats;
      }

      // 数据加载完成后计算动态分页和检测滚动需求
      calculateDynamicPages();
      checkScrollNeed();
    }
  };

  eventSource.onerror = (err) => {
    console.error('SSE连接错误:', err);
  };
};

const dutyData = ref([]);







// 生命周期钩子
onMounted(() => {
  try {

    // 确保DOM元素存在后再初始化ECharts
    if (chartContainer.value) {
      barChart = echarts.init(chartContainer.value);
      initChart();
    }



    setupSSE();

    // 立即获取统计数据
    fetchAlarmStatistics();
    // 然后开始定时轮询
    startPolling();

    // 移除地图初始化代码


  paginationTimer = setInterval(nextPage, 15000);
  importantEventsPaginationTimer = setInterval(nextImportantEventsPage, 15000);

  // 检测状态统计滚动需求
  checkScrollNeed();

  // 添加窗口大小变化监听
  const handleResize = () => {
    calculateDynamicPages();
    checkScrollNeed();
  };
  window.addEventListener('resize', handleResize);

  // 初始计算动态分页
  setTimeout(calculateDynamicPages, 2000);

  // 移除地图相关初始化
  } catch (error) {
    console.error('组件初始化失败:', error);
  }
});

onBeforeUnmount(() => {
  if (eventSource) {
    eventSource.close();
  }
});

onUnmounted(() => {
  try {
    // 清理所有定时器
    clearInterval(paginationTimer);
    clearInterval(importantEventsPaginationTimer);


    if (pollTimer) {
      clearInterval(pollTimer);
      pollTimer = null;
    }
    // 移除地图定时器清理

    // 移除地图清理代码
    if (barChart && !barChart.isDisposed()) {
      barChart.dispose();
      barChart = null;
    }
  } catch (error) {
    console.error('组件清理失败:', error);
  }
  // 清理resize事件监听器
  window.removeEventListener('resize', () => {
    if (barChart) barChart.resize();
  });

  // 清理动态分页计算的事件监听器
  window.removeEventListener('resize', calculateDynamicPages);

  // 清理全屏事件监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
});



const startPolling = () => {
  pollTimer = setInterval(fetchAlarmStatistics, 10000);
};

// 统一的图表更新函数
const updateChartsWithData = (otherStatistics) => {
  try {
    // 检查组件是否已销毁
    if (!chartContainer.value) {
      console.warn('图表容器已销毁，跳过更新');
      return;
    }



    if (barChart && !barChart.isDisposed()) {
      barChart.setOption({
        xAxis: {
          type: 'category',
          data: otherStatistics.map(item => item[0])
        },
        series: [{
          type: 'bar',
          data: otherStatistics.map(item => item[1])
        }]
      });
    }
  } catch (error) {
    console.error('更新图表失败:', error);
  }
};

const fetchAlarmStatistics = async () => {
  if (DATA_CONFIG.USE_MOCK_DATA) {
    // Mock模式：使用模拟数据
    console.log('[Mock模式] 获取警情统计数据')
    const [minDelay, maxDelay] = DATA_CONFIG.MOCK_CONFIG.STATISTICS_DELAY
    await new Promise(resolve => setTimeout(resolve, Math.random() * (maxDelay - minDelay) + minDelay))

    const mockData = generateMockAlarmStatistics()
    const otherStatistics = mockData.categories.map(item => [item.name, item.count]);

    // 更新图表
    updateChartsWithData(otherStatistics);
    return;
  }

  // 生产模式：使用真实接口
  console.log('[生产模式] 从接口获取警情统计数据')

  // 获取当天的时间范围
  const timeRange = getTodayTimeRange()
  const currentStartTime = timeRange.startTime
  const currentEndTime = timeRange.endTime

  // 基础URL参数
  const baseParams = {
    page: 1,
    size: 1,
    orderSet: '',
    alarmNum: '',
    receiveType: '',
    searchStr: '',
    governOrgCode: '511621000000',
    isIncludeSubGovernOrg: true,
    receiveUserName: '',
    callType: '',
    receiveOrgCode: '',
    alarmCategoryCode: '',
    alarmTypeCode: '',
    alarmSubclassCode: '',
    alarmDetailCode: '',
    modifyAlarmCategoryCode: '',
    modifyAlarmTypeCode: '',
    modifyAlarmSubclassCode: '',
    modifyAlarmDetailCode: '',
    isSign: '',
    isOverFeedback: '',
    alarmLevel: '',
    isValidAlarm: '',
    dealUserId: '',
    modifyGovernOrgCode: '',
    dealOrgCode: '',
    jqsla: '',
    alarmMode: '',
    startTime: currentStartTime,
    endTime: currentEndTime,
    orgCode: '511621000000',
    feedbackContentSize: null
  }

  // 构建URL的辅助函数
  const buildUrl = (params) => {
    const urlParams = { ...baseParams, ...params }
    const queryString = Object.entries(urlParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value || '')}`)
      .join('&')
    return `http://80.164.4.207:8000/founder-IntegratedCommand-storage/v0.1/alarm/storage?${queryString}`
  }

  const alarmTypes = [
    { name: '盗窃', url: buildUrl({ modifyAlarmCategoryCode: '01000000', modifyAlarmTypeCode: '01040000', modifyAlarmSubclassCode: '01040300' }) },
    { name: '接触性诈骗', url: buildUrl({ modifyAlarmCategoryCode: '01000000', modifyAlarmTypeCode: '01040000', modifyAlarmSubclassCode: '01040400' }) },
    { name: '电信诈骗', url: buildUrl({ modifyAlarmCategoryCode: '01000000', modifyAlarmTypeCode: '01040000', modifyAlarmSubclassCode: '01040500' }) },
    { name: '抢劫', url: buildUrl({ modifyAlarmCategoryCode: '01000000', modifyAlarmTypeCode: '01040000', modifyAlarmSubclassCode: '01040100' }) },
    { name: '其他刑事', url: buildUrl({ modifyAlarmCategoryCode: '01000000', modifyAlarmTypeCode: '01990000' }) },
    { name: '治安案件', url: buildUrl({ modifyAlarmCategoryCode: '02000000' }) },
    { name: '交通事故', url: buildUrl({ modifyAlarmCategoryCode: '03000000' }) },
    { name: '火灾', url: buildUrl({ modifyAlarmCategoryCode: '06000000', modifyAlarmTypeCode: '06080000', modifyAlarmSubclassCode: '06080100' }) },
    { name: '纠纷', url: buildUrl({ modifyAlarmCategoryCode: '08000000' }) },
    { name: '举报', url: buildUrl({ modifyAlarmCategoryCode: '09000000' }) },
    { name: '群众求助', url: buildUrl({ modifyAlarmCategoryCode: '06000000' }) },
    { name: '其他', url: buildUrl({ modifyAlarmCategoryCode: '12000000' }) },
    { name: '有效警情', url: buildUrl({ isValidAlarm: true }) },
    { name: '无效警情', url: buildUrl({ isValidAlarm: false }) }
  ];

  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const today = formatDate(new Date());
  const startTime = `${today} 00:00:00`;
  const endTime = `${today} 23:59:59`;

  const fetchAlarmData = (type) => {
    return new Promise((resolve, reject) => {
      let url = type.url.replace(/startTime=[^&]+/, `startTime=${encodeURIComponent(startTime)}`)
                        .replace(/endTime=[^&]+/, `endTime=${encodeURIComponent(endTime)}`);

      fetch(url)
        .then(response => response.ok ? response.json() : Promise.reject(`请求失败: ${response.status}`))
        .then(data => resolve({ name: type.name, count: data.data?.count || 0 }))
        .catch(error => reject({ name: type.name, error: error.message }));
    });
  };

  try {
    const results = await Promise.all(alarmTypes.map(fetchAlarmData));
    const [contactFraud, telecomFraud] = results.filter(item => ['接触性诈骗','电信诈骗'].includes(item.name));
    const fraudCount = (contactFraud?.count || 0) + (telecomFraud?.count || 0);
    // 提取'其他'项（原数组中可能存在多个，这里假设只有一个）
const otherItem = results.find(item => item.name === '其他');

// 构建合并数组时排除'接触性诈骗'、'电信诈骗'和'其他'
const mergedResults = [
  ...results.filter(item => !['接触性诈骗','电信诈骗','其他'].includes(item.name)),
  { name: '诈骗', count: fraudCount }
];

// 将'其他'项添加到数组末尾
if (otherItem) mergedResults.push(otherItem);



    // 定义固定的警情分类顺序
    const fixedOrder = [
      '盗窃', '诈骗', '抢劫', '其他刑事', '治安案件',
      '交通事故', '火灾', '纠纷', '举报', '群众求助', '其他'
    ];

    // 按照固定顺序重新排列数据
    const orderedStatistics = fixedOrder.map(categoryName => {
      const found = mergedResults.find(item => item.name === categoryName);
      return [categoryName, found ? found.count : 0];
    });

    // 更新图表
    updateChartsWithData(orderedStatistics);
  } catch (error) {
    console.error('获取警情数据失败:', error);
  }
};
</script>

<style scoped>


.holographic-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #26d0ce;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}












.hologram-frame {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  pointer-events: none;
  z-index: 0;
}

.hologram-frame::before,
.hologram-frame::after,
.hologram-frame > div::before,
.hologram-frame > div::after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border-color: #00e5ff;
  border-style: solid;
}

.hologram-frame::before { top: -2px; left: -2px; border-width: 2px 0 0 2px; }
.hologram-frame::after { top: -2px; right: -2px; border-width: 2px 2px 0 0; }
.hologram-frame > div::before { bottom: -2px; left: -2px; border-width: 0 0 2px 2px; }
.hologram-frame > div::after { bottom: -2px; right: -2px; border-width: 0 2px 2px 0; }


@keyframes float {
  0% { transform: translate(0, 0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translate(30px, 50px); opacity: 0; }
}
.grid-container {
    width: 100%;
    height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 20px;
    padding: 20px;
    box-sizing: border-box;
    overflow: hidden;
}

/* 网格项目定位和样式 */
.grid-item-persons {
    grid-column: 1;
    grid-row: 1 / 3; /* 占第1-2行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grid-item-duty {
    grid-column: 1;
    grid-row: 3; /* 占第3行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grid-item-events {
    grid-column: 3;
    grid-row: 1 / 3; /* 占第1-2行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grid-item-chart {
    grid-column: 3;
    grid-row: 3; /* 占第3行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grid-item-blank1 {
    grid-column: 2;
    grid-row: 1 / 3; /* 占第1-2行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grid-item-blank2 {
    grid-column: 2;
    grid-row: 3; /* 占第3行 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 空白容器样式 */
.blank-container {
    background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
    border: 2px solid rgba(0, 229, 255, 0.6);
    border-radius: 15px;
    color: #c0e0ff;
    font-family: "Microsoft YaHei", sans-serif;
    padding: 20px;
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 229, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.blank-container:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 229, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 通用容器样式 */
.duty-table-container {
  background: rgb(17, 27, 71, 0.5);
  border: 1px solid rgba(100, 200, 255, 0.5);
  border-radius: 15px;
  color: #c0e0ff;
  font-family: "Microsoft YaHei", sans-serif;
  padding: 20px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 229, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.duty-table-title {
  flex: 0;
  margin: 10px;
  /* 美化后的样式 */
  color: #00e5ff; /* 科技蓝色 */
  font-size: 20px; /* 标题字体大小 */
  font-weight: bold; /* 标题字体加粗 */
  font-family: 'Orbitron', sans-serif; /* 科技字体 */
  text-align: center; /* 标题居中 */
  padding: 8px; /* 保持紧凑的内边距 */
  min-height: 20px; /* 较小的最小高度 */
  background: linear-gradient(135deg, rgba(0, 229, 255, 0.15), rgba(38, 208, 206, 0.1)); /* 渐变背景 */
  border: 2px solid rgba(0, 229, 255, 0.6); /* 发光边框 */
  border-radius: 12px; /* 圆角 */
  backdrop-filter: blur(8px); /* 毛玻璃效果 */
  box-shadow:
    0 4px 20px rgba(0, 229, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 发光阴影 */
  text-shadow:
    0 0 10px rgba(0, 229, 255, 0.8),
    0 0 20px rgba(0, 229, 255, 0.4); /* 文字发光 */
  transition: all 0.3s ease; /* 过渡动画 */
  position: relative;
  overflow: hidden;
}

/* 标题悬停效果 */
.duty-table-title:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(0, 229, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow:
    0 0 15px rgba(0, 229, 255, 1),
    0 0 30px rgba(0, 229, 255, 0.6);
}

/* 标题光效动画 */
.duty-table-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 229, 255, 0.2), transparent);
  transition: left 0.8s ease;
}

.duty-table-title:hover::before {
  left: 100%;
}
.duty-table {
  flex: 1;
  /* 新增样式 */
  width: 100%; /* 表格宽度 */
  border-collapse: collapse; /* 合并边框 */
  color: #c0e0ff; /* 表格文字颜色 */
}
.duty-table th, .duty-table td {
  text-align: center; /* 文字水平居中 */
  vertical-align: middle; /* 文字垂直居中 */
}
.duty-table td {
  text-align: center; /* 文字居中 */
  color: white; /* 浅绿色文字 */
  font-family: 'Orbitron', sans-serif;  /* 圆润字体 */
}
.duty-table th {
  color: white; /* 浅绿色文字 */
  background-color: rgba(17, 27, 71, 0.6); /* 表头背景颜色 */
  font-weight: bold; /* 表头文字加粗 */
}
.duty-table tr:nth-child(even) {
  background-color: rgba(17, 27, 71, 0.3); /* 偶数行背景颜色 */
}

/* 饼图包装器 */
.pie-chart-wrapper {
  background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
  border: 2px solid rgba(0, 229, 255, 0.6);
  border-radius: 15px;
  color: #c0e0ff;
  font-family: "Microsoft YaHei", sans-serif;
  z-index: 100;
  grid-row: 25 / 37;
  grid-column: 10 / 19;
  margin: 10px;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 229, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.pie-chart-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 229, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pie-chart-container {
  flex: 1;
  min-height: 0;
}

/* 柱状图包装器 */
.bar-chart-wrapper {
  background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
  border: 2px solid rgba(0, 229, 255, 0.6);
  border-radius: 15px;
  color: #c0e0ff;
  font-family: "Microsoft YaHei", sans-serif;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 229, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.bar-chart-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 229, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.chart-container {
  flex: 1;
  min-height: 0;
}
.important-events {
  background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
  border: 2px solid rgba(0, 229, 255, 0.6);
  border-radius: 15px;
  color: #c0e0ff;
  font-family: "Microsoft YaHei", sans-serif;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 229, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  height: 100%;
  min-height: 0;
}

.important-events:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 229, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.important-persons {
    background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
    border: 2px solid rgba(0, 229, 255, 0.6);
    border-radius: 15px;
    color: #c0e0ff;
    font-family: "Microsoft YaHei", sans-serif;
    padding: 20px;
    padding-bottom: 80px; /* 为底部状态统计留出空间 */
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 229, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative; /* 添加相对定位 */
    height: 100%;
  }

  .important-persons:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 229, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .person-card {
    margin: 12px 8px;
    background: linear-gradient(135deg, rgba(5, 15, 44, 0.8), rgba(26, 41, 128, 0.4));
    border: 1px solid rgba(0, 229, 255, 0.8);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow:
      0 4px 20px rgba(0, 229, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .person-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 229, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }

  .person-card:hover::before {
    left: 100%;
  }

  .person-card:hover {
    transform: translateY(-3px) scale(1.02);
    border-color: rgba(0, 229, 255, 1);
    box-shadow:
      0 8px 30px rgba(0, 229, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* 定义进场和离场动画 */
  .person-card-enter-active,
  .person-card-leave-active {
    transition: opacity 0.5s, transform 0.5s;
  }

  .person-card-enter-from,
  .person-card-leave-to {
    opacity: 0;
    transform: translateX(20px);
  }
.important-events-title-containter,
.important-persons-title-containter {
  display: flex; /* 使用弹性布局 */
  align-items: center; /* 垂直居中对齐 */
  justify-content: space-between; /* 左右分布 */
  background: linear-gradient(135deg, rgba(0, 229, 255, 0.1), rgba(38, 208, 206, 0.05)); /* 渐变背景 */
  border: 2px solid rgba(0, 229, 255, 0.5); /* 发光边框 */
  border-radius: 12px; /* 增大圆角 */
  font-size: 16px; /* 字体大小 */
  color: #00e5ff; /* 科技蓝色 */
  font-weight: bold; /* 字体加粗 */
  font-family: 'Orbitron', sans-serif; /* 科技字体 */
  padding: 6px; /* 保持原来的内边距 */
  gap: 10px;
  overflow: hidden;
  backdrop-filter: blur(5px); /* 毛玻璃效果 */
  box-shadow:
    0 4px 15px rgba(0, 229, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 发光阴影 */
  transition: all 0.3s ease; /* 过渡动画 */
  position: relative;
}

/* 标题容器悬停效果 */
.important-events-title-containter:hover,
.important-persons-title-containter:hover {
  transform: translateY(-1px);
  box-shadow:
    0 6px 25px rgba(0, 229, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 标题左侧区域样式 */
.title-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 页码信息样式 */
.pagination-info {
  font-size: 14px;
  color: #88b7e0;
  font-weight: normal;
  background: rgba(0, 102, 204, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(100, 200, 255, 0.3);
}

.el-icon {
  font-size: 24px; /* 图标大小 */
  color: #00e5ff; /* 图标颜色 */
  filter: drop-shadow(0 0 8px rgba(0, 229, 255, 0.6)); /* 图标发光效果 */
}

/* 重要事件标题文字样式 */
.important-events-title {
  font-size: 18px;
  font-weight: bold;
  font-family: 'Orbitron', sans-serif;
  color: #00e5ff;
  text-shadow:
    0 0 8px rgba(0, 229, 255, 0.8),
    0 0 16px rgba(0, 229, 255, 0.4);
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.important-events-title:hover {
  text-shadow:
    0 0 12px rgba(0, 229, 255, 1),
    0 0 24px rgba(0, 229, 255, 0.6);
}

/* 重要人员标题文字样式 */
.important-persons-title {
  font-size: 18px;
  font-weight: bold;
  font-family: 'Orbitron', sans-serif;
  color: #00e5ff;
  text-shadow:
    0 0 8px rgba(0, 229, 255, 0.8),
    0 0 16px rgba(0, 229, 255, 0.4);
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.important-persons-title:hover {
  text-shadow:
    0 0 12px rgba(0, 229, 255, 1),
    0 0 24px rgba(0, 229, 255, 0.6);
}
.event-card {
    background: linear-gradient(135deg, rgba(5, 15, 44, 0.8), rgba(26, 41, 128, 0.4));
    border: 1px solid rgba(0, 229, 255, 0.8);
    border-radius: 12px;
    color: #fff;
    margin: 12px 8px;
    padding: 0px !important;
    backdrop-filter: blur(8px);
    box-shadow:
      0 4px 20px rgba(0, 229, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 229, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.event-card:hover::before {
  left: 100%;
}

.event-card:hover {
  transform: translateY(-3px) scale(1.02);
  border-color: rgba(0, 229, 255, 1);
  box-shadow:
    0 8px 30px rgba(0, 229, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 进一步减少事件卡片的内边距 */
.event-card ::v-deep .el-card__body {
  padding: 10px 15px 5px 15px !important; /* 上右下左，特别减少底部内边距 */
}
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .el-timeline-item__timestamp {
    color: #9e4edd;
  }

  .person-card {
    margin: 10px;
    background-color: rgba(5, 15, 44, 0.5);
    border: 1px solid #00e5ff;
    color: #fff;
    padding: 0px !important;
  }

/* 减少人员卡片的内边距 */
.person-card ::v-deep .el-card__body {
  padding: 10px 15px 5px 15px !important; /* 上右下左，特别减少底部内边距 */
}

/* 重点人员卡片样式重新设计 */
.person-name {
  font-weight: bold;
  font-size: 16px;
  color: #00e5ff;
  text-shadow: 0 0 8px rgba(0, 229, 255, 0.6);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
}

.person-area {
  color: #1890ff;
  font-size: 14px;
  font-weight: 500;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.person-type {
  color: #52c41a;
  font-size: 14px;
  font-weight: 500;
  background: rgba(82, 196, 26, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.person-status {
  color: #faad14;
  font-size: 14px;
  font-weight: 500;
  background: rgba(250, 173, 20, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

/* 重大案事件卡片样式重新设计 - 按等级区分 */
/* 等级1 - 最高等级（红色系） */
.event-name-critical {
  font-weight: bold;
  font-size: 17px;
  color: #ff4757;
  text-shadow: 0 0 6px rgba(255, 71, 87, 0.4);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
}

/* 等级2 - 高等级（橙色系） */
.event-name-high {
  font-weight: bold;
  font-size: 17px;
  color: #fa8c16;
  text-shadow: 0 0 6px rgba(250, 140, 22, 0.4);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
}

/* 等级3 - 中等级（黄色系） */
.event-name-medium {
  font-weight: bold;
  font-size: 17px;
  color: #faad14;
  text-shadow: 0 0 6px rgba(250, 173, 20, 0.4);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
}

/* 普通等级（蓝色系） */
.event-name-normal {
  font-weight: bold;
  font-size: 17px;
  color: #00e5ff;
  text-shadow: 0 0 6px rgba(0, 229, 255, 0.4);
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
}

/* 区域标签按等级区分 */
.event-area-critical {
  color: #ff4757;
  font-size: 14px;
  font-weight: 500;
  background: rgba(255, 71, 87, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.event-area-high {
  color: #fa8c16;
  font-size: 14px;
  font-weight: 500;
  background: rgba(250, 140, 22, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(250, 140, 22, 0.3);
}

.event-area-medium {
  color: #faad14;
  font-size: 14px;
  font-weight: 500;
  background: rgba(250, 173, 20, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.event-area-normal {
  color: #1890ff;
  font-size: 14px;
  font-weight: 500;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

/* 状态标签按等级区分 */
.event-status-critical {
  color: #ff4757;
  font-size: 14px;
  font-weight: 500;
  background: rgba(255, 71, 87, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.event-status-high {
  color: #fa8c16;
  font-size: 14px;
  font-weight: 500;
  background: rgba(250, 140, 22, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(250, 140, 22, 0.3);
}

.event-status-medium {
  color: #faad14;
  font-size: 14px;
  font-weight: 500;
  background: rgba(250, 173, 20, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.event-status-normal {
  color: #52c41a;
  font-size: 14px;
  font-weight: 500;
  background: rgba(82, 196, 26, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.event-details {
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 15px;
  line-height: 1.5;
  color: #e6f4ff;
  /* 限制最大显示2行 */
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-break: break-word;
}

/* 具体内容标签按等级区分 */
.details-label-critical {
  color: #ff4757;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(255, 71, 87, 0.4);
}

.details-label-high {
  color: #fa8c16;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(250, 140, 22, 0.4);
}

.details-label-medium {
  color: #faad14;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(250, 173, 20, 0.4);
}

.details-label-normal {
  color: #00e5ff;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(0, 229, 255, 0.4);
}

/* 人员卡片时间轴容器收紧底部空间 */
.person-card .custom-timeline {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 人员卡片最后一个时间轴项目减少底部间距 */
.person-card .custom-timeline .el-timeline-item:last-child ::v-deep .el-timeline-item__content {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 人员卡片最后一个时间轴项目的整体底部间距 */
.person-card .custom-timeline .el-timeline-item:last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .el-timeline-item__timestamp {
    color: red;
  }

  .el-timeline-item__content {
    color: #9e4edd;
  }

  .map-container {
    grid-row: 1 / 25;
    grid-column: 10 / 25;
    margin: 10px;
    margin-top: 30px;
    background: linear-gradient(135deg, rgba(17, 27, 71, 0.8), rgba(26, 41, 128, 0.6));
    border: 2px solid rgba(0, 229, 255, 0.6);
    border-radius: 15px;
    position: relative;
    display: flex;
    flex-direction: column;
    z-index: 100;
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 229, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .map-container:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 229, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .map-chart {
    flex: 1;
    position: relative;
    min-height: 0;
    z-index: 101;
  }

/* 强制隐藏所有地图相关的tooltip */
.map-chart ::v-deep .echarts-tooltip {
  display: none !important;
}

/* 隐藏所有ECharts的tooltip */
::v-deep .echarts-tooltip {
  display: none !important;
}

/* 隐藏所有可能的tooltip容器 */
::v-deep div[class*="tooltip"] {
  display: none !important;
}

/* 隐藏所有包含地名的tooltip */
::v-deep div[style*="岳池"] {
  display: none !important;
}

/* 强制确保地图背景色不变 */
.map-chart ::v-deep canvas {
  background-color: transparent !important;
}

/* 确保地图容器背景色稳定 */
.map-chart {
  background-color: transparent !important;
}

/* Level等级圆点样式 */
.level-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 8px;
  vertical-align: middle;
}

.level-red {
  background-color: #ff073a;
}

.level-orange {
  background-color: orange;
}

.level-yellow {
  background-color: yellow;
}

.level-default {
  background-color: #c41e3a;
}

  /* 地图错误信息样式 */
  .map-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ff4d4d;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    z-index: 1000;
  }


  /* 定义重大事件进场和离场动画 */
.event-card-enter-active,
.event-card-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.event-card-enter-from,
.event-card-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.event-card {
  transition: all 0.5s ease-in-out;
}

/* 优化版时间线样式 */


::v-deep .custom-timeline .el-timeline-item__node {
  /* 时间线标记优化 */
  width: 10px; /* 增大标记圆点 */
  height: 10px; 
  background: #0066cc; /* 使用项目主色 */
  border: 2px solid rgba(0, 102, 204, 0.3); /* 增加边框层次感 */
}

::v-deep .custom-timeline .el-timeline-item__timestamp {
  color: #88b7e0; /* 时间戳使用主色浅蓝，与整体风格统一 */
  font-size: 13px; /* 字体稍大更易读 */
  margin-top: -4px; /* 微调时间戳与内容的垂直对齐 */
  margin-bottom: 20px; /* 进一步加大时间戳与处置措施之间的间距 */
}

::v-deep .custom-timeline .el-timeline-item__content {
  color: #e6f4ff; /* 内容文字使用浅蓝，与背景对比度更高 */
  line-height: 1.7; /* 行高稍大提升阅读流畅度 */
  margin-bottom: 15px; /* 增加底部间距 */
  margin-top: 20px; /* 进一步加大处置措施与时间戳之间的间距 */
  background: rgba(0, 102, 204, 0.1); /* 背景使用主色浅透明，与整体风格统一 */
  border-radius: 6px; /* 圆角增大更柔和 */
  transition: all 0.3s; /* 添加过渡动画 */
}

/* 鼠标悬停效果（可选增强交互） */
::v-deep .custom-timeline .el-timeline-item__content:hover {
  background: rgba(0, 102, 204, 0.15); /* 悬停时背景加深 */
  transform: translateX(5px); /* 轻微右移增强反馈 */
}

/* 紫红色标签样式 */
.purple-tag {
  color: #c41e3a !important; /* 紫红色文字 */
}

/* 处置情况区域样式 */
.disposal-section {
  margin-top: 15px;
  padding: 12px;
  background: rgba(0, 102, 204, 0.05);
  border-radius: 8px;
}

/* 处置区域左边框按等级区分 */
.disposal-section-critical {
  border-left: 4px solid #ff4757;
}

.disposal-section-high {
  border-left: 4px solid #fa8c16;
}

.disposal-section-medium {
  border-left: 4px solid #faad14;
}

.disposal-section-normal {
  border-left: 4px solid #00e5ff;
}

.disposal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

/* 处置标题按等级区分 */
.disposal-title-critical {
  font-size: 15px;
  font-weight: bold;
  color: #ff4757;
}

.disposal-title-high {
  font-size: 15px;
  font-weight: bold;
  color: #fa8c16;
}

.disposal-title-medium {
  font-size: 15px;
  font-weight: bold;
  color: #faad14;
}

.disposal-title-normal {
  font-size: 15px;
  font-weight: bold;
  color: #00e5ff;
}

.disposal-time {
  font-size: 14px;
  color: #88b7e0;
  font-weight: 500;
}

/* 处置内容样式 - 强制4行截断 */
.disposal-section .disposal-content,
.disposal-section-critical .disposal-content,
.disposal-section-high .disposal-content,
.disposal-section-medium .disposal-content,
.disposal-section-normal .disposal-content {
  color: #e6f4ff;
  line-height: 1.6 !important;
  font-size: 14px;
  padding: 10px;
  background: rgba(0, 102, 204, 0.1);
  border-radius: 6px;
  margin-top: 5px;
  /* 强制4行截断 */
  height: calc(1.6em * 4) !important;
  max-height: calc(1.6em * 4) !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  word-break: break-word;
  white-space: normal !important;
  /* 使用webkit-line-clamp作为辅助 */
  display: -webkit-box !important;
  -webkit-line-clamp: 4 !important;
  line-clamp: 4 !important;
  -webkit-box-orient: vertical !important;
}

/* 人员动向区域样式 */
.movement-section {
  margin-top: 15px;
  padding: 12px;
  background: rgba(0, 102, 204, 0.05);
  border-radius: 8px;
  border-left: 4px solid #00e5ff;
}

.movement-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.movement-title {
  font-size: 15px;
  font-weight: bold;
  color: #00e5ff;
}

.movement-time {
  font-size: 14px;
  color: #88b7e0;
  font-weight: 500;
}

.movement-content {
  color: #e6f4ff;
  line-height: 1.6;
  font-size: 14px;
  padding: 8px 0;
  background: rgba(0, 102, 204, 0.1);
  border-radius: 6px;
  padding: 10px;
  margin-top: 5px;
}

/* 辖区统计表格样式 */
.area-statistics-table {
  flex: 1;
  background: rgba(17, 27, 71, 0.3);
  border: 1px solid rgba(100, 200, 255, 0.5);
  border-radius: 8px;
  padding: 20px;
  color: white;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.table-header h3 {
  margin: 0;
  color: #0066cc;
  font-size: 18px;
  font-weight: bold;
}

.table-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 表格样式覆盖 */
.area-statistics-table ::v-deep .el-table {
  background: transparent;
  color: white;
  height: 100%;
}

.area-statistics-table ::v-deep .el-table__inner-wrapper {
  height: 100%;
}

.area-statistics-table ::v-deep .el-table__body-wrapper {
  flex: 1;
  overflow-y: auto;
}

.area-statistics-table ::v-deep .el-table th {
  background: #0b153a;
  color: white;
  border-color: rgba(100, 200, 255, 1);
  font-weight: bold;
  font-size: 16px !important; /* 加大表头文字 */
}

.area-statistics-table ::v-deep .el-table td {
  background: #0b153a !important;
  border-color: rgba(100, 200, 255, 0.2);
  color: white !important;
  font-size: 15px !important; /* 加大表格内容文字 */
}

/* 覆盖斑马纹样式，确保所有行都是深蓝色背景 */
.area-statistics-table ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #0b153a !important;
}

.area-statistics-table ::v-deep .el-table__body tr:hover > td {
  background: #0b153a !important;
}

/* 确保表格内所有文字都是白色并加大字体 */
.area-statistics-table ::v-deep .el-table td span {
  color: white !important;
  font-size: 15px !important; /* 加大span元素文字 */
  font-weight: bold !important; /* 加粗文字 */
}

.area-statistics-table ::v-deep .el-table .cell {
  color: white !important;
  font-size: 15px !important; /* 加大单元格文字 */
  font-weight: bold !important; /* 加粗文字 */
}



/* 降低表格行高度 */
.area-statistics-table ::v-deep .el-table td,
.area-statistics-table ::v-deep .el-table th {
  padding: 4px 8px;
  height: 36px;
}

.area-statistics-table ::v-deep .el-table .cell {
  line-height: 1.0;
  padding: 1px 4px;
}

/* 人员状态统计滚动样式 */
.status-statistics-container {
  position: absolute;
  bottom: 10px;
  left: 15px;
  right: 15px;
  height: 60px;
  overflow: hidden;
  background: rgba(17, 27, 71, 0.8);
  border: 1px solid rgba(100, 200, 255, 0.5);
  border-radius: 8px;
  z-index: 10;
  margin: 0 8px; /* 增加边距，减小宽度 */
}

.status-statistics-scroll {
  display: flex;
  height: 100%;
  align-items: center;
  white-space: nowrap;
  transition: transform 0.3s ease;
}

/* 只有在需要滚动时才添加动画 */
.status-statistics-scroll.scrolling {
  animation: scrollLeftContinuous 8s linear infinite;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 90px;
  margin: 0 12px;
  padding: 6px 12px;
  background: linear-gradient(135deg, rgba(0, 229, 255, 0.2), rgba(26, 41, 128, 0.3));
  border: 1px solid rgba(0, 229, 255, 0.4);
  border-radius: 6px;
  backdrop-filter: blur(5px);
}

.status-name {
  font-size: 14px;
  color: #00e5ff;
  font-weight: bold;
  margin-bottom: 2px;
}

.status-count {
  font-size: 13px;
  color: white;
  font-weight: bold;
}

@keyframes scrollLeftContinuous {
  0% {
    transform: translateX(calc(100% + 120px));
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 鼠标悬停时暂停动画 */
.status-statistics-container:hover .status-statistics-scroll {
  animation-play-state: paused;
}

</style>

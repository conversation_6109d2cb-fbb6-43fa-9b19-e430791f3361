<template>
    <el-container class="portal-container">
      <el-header class="header">
        <div class="header-left">
          <img src="../assets/imgs/logo.png" alt="Logo" class="header-logo">
          <span class="logo">CloudPivot</span>
        </div>
        <div class="header-right">
          <span class="version">v.1.7.3</span>
          <el-button type="text" @click="showUpdateLog" class="update-log-btn">
            更新日志
          </el-button>
        </div>
      </el-header>
  
      <el-main>
        <div class="category-container">
          <!-- 公用应用 -->
          <div class="app-category">
            <h2 class="category-title">公用应用</h2>
            <div class="card-grid">
              <el-card 
                v-for="app in publicApps"
                :key="app.id"
                class="app-card"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="card-content" @click="navigateTo(app.path)">
                  <div class="app-icon" :style="{ background: app.color }">
                    <span v-if="app.icon" class="icon-emoji">{{ app.icon }}</span>
                  </div>
                  <div class="app-info">
                    <h3>{{ app.name }}</h3>
                    <p>{{ app.desc }}</p>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
          
          <!-- 专用应用 -->
          <div class="app-category">
            <h2 class="category-title">专用应用</h2>
            <div class="card-grid">
              <el-card 
                v-for="app in privateApps"
                :key="app.id"
                class="app-card"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="card-content" @click="navigateTo(app.path)">
                  <div class="app-icon" :style="{ background: app.color }">
                    <span v-if="app.icon" class="icon-emoji">{{ app.icon }}</span>
                  </div>
                  <div class="app-info">
                    <h3>{{ app.name }}</h3>
                    <p>{{ app.desc }}</p>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
          
          <!-- 系统应用 -->
          <div class="app-category">
            <h2 class="category-title">系统应用</h2>
            <div class="card-grid">
              <el-card 
                v-for="app in systemApps"
                :key="app.id"
                class="app-card"
                :body-style="{ padding: '0px' }"
                shadow="hover"
              >
                <div class="card-content" @click="navigateTo(app.path)">
                  <div class="app-icon" :style="{ background: app.color }">
                    <span v-if="app.icon" class="icon-emoji">{{ app.icon }}</span>
                  </div>
                  <div class="app-info">
                    <h3>{{ app.name }}</h3>
                    <p>{{ app.desc }}</p>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-main>
  
      <el-footer class="portal-footer">
        <p>Powered by ：科技通信中队</p>
      </el-footer>
    </el-container>

    <!-- 更新日志对话框 -->
    <el-dialog
      v-model="updateLogVisible"
      title="更新日志"
      width="500px"
      :close-on-click-modal="false"
      custom-class="update-log-dialog"
    >
      <div class="update-log-content">
        

        <div class="version-item">
          <div class="version-header">
            <h3>v.1.7.3</h3>
            <span class="release-date">2025年08月07日</span>
          </div>
          <ul>
            <li>上线应用：通讯录管理</li>
            <li>优化主页图标</li>
            <li>修复部分已知BUG</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button @click="updateLogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </template>
  
  <style scoped>
  .portal-container {
    height: 100vh;
    background: #f5f7fa;
    overflow: hidden;
  }

  .el-main {
    height: calc(100vh - 160px); /* 减去header(100px)和footer(60px)的高度 */
    overflow-y: auto;
    padding: 0;
    flex: 1;
  }

  .header {
    height: 100px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    background-image: linear-gradient(to right, #bd200b, #f10d51);
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    z-index: 10;
    overflow: visible;
    color: white;
    flex-shrink: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    flex: 0 1 auto;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .version {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  .update-log-btn {
    color: white !important;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .update-log-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .header-logo {
    height: 36px;
    margin-right: 10px;
  }

  .logo {
    font-size: 20px;
    font-weight: bold;
    color: white;
  }

  /* 响应式调整 */
  @media screen and (max-width: 768px) {
    .header {
      padding: 0 10px;
    }

    .logo {
      font-size: 16px;
    }
  }
  
  .category-container {
    display: flex;
    gap: 20px;
    padding: 24px;
    min-height: 100%;
    box-sizing: border-box;
  }
  
  .app-category {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .category-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }
  
  .card-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
  
  .app-card {
    margin-bottom: 0;
    transition: transform 0.3s, box-shadow 0.3s;
    min-width: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .app-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .card-content {
    padding: 24px;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: white;
    border-radius: 12px;
  }
  
  .app-icon {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
  }
  
  .app-card:hover .app-icon {
    transform: scale(1.05);
  }

  .icon-emoji {
    font-size: 32px;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
  }

  /* 更新日志对话框样式 */
  :deep(.update-log-dialog) {
    height: 600px;
  }

  :deep(.update-log-dialog .el-dialog__body) {
    height: 480px;
    padding: 20px;
  }

  /* 更新日志样式 */
  .update-log-content {
    height: 100%;
    overflow-y: auto;
    padding-top: 15px; /* 为"即将上线"标签留出空间 */
  }

  .version-item {
    margin-bottom: 20px;
    border: 2px solid #667eea;
    border-radius: 8px;
    padding: 15px;
    background: linear-gradient(135deg, #f0f2ff 0%, #f8f9ff 100%);
    position: relative;
  }

  .version-item.upcoming {
    border-color: #faad14;
    border-style: dashed;
    background: linear-gradient(135deg, #fff7e6 0%, #fffbf0 100%);
  }

  .version-item::before {
    position: absolute;
    top: -8px;
    right: 15px;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .version-item::before {
    content: "已上线";
    background: #52c41a;
  }

  .version-item.upcoming::before {
    content: "即将上线";
    background: #faad14;
  }

  .version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .version-item h3 {
    color: #667eea;
    font-size: 18px;
    margin: 0;
    font-weight: 600;
  }

  .version-item.upcoming h3 {
    color: #faad14;
  }

  .release-date {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    color: white;
    background: #52c41a;
  }

  .upcoming-date {
    background: #faad14;
    color: white;
    font-weight: 600;
  }

  .version-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .version-item li {
    padding: 8px 0;
    border-left: 3px solid #667eea;
    padding-left: 15px;
    margin-bottom: 5px;
    background: #f8f9ff;
    border-radius: 0 6px 6px 0;
    color: #333;
    font-size: 14px;
  }

  .version-item li:before {
    content: "✓";
    color: #52c41a;
    font-weight: bold;
    margin-right: 8px;
  }

  .version-item.upcoming li {
    border-left-color: #faad14;
    background: #fffbf0;
    color: #d48806;
  }

  .version-item.upcoming li:before {
    content: "🔮";
    color: #faad14;
  }
  
  .app-info {
    text-align: center;
  }
  
  .app-info h3 {
    color: #303133;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-weight: 500;
  }
  
  .app-info p {
    color: #909399;
    font-size: 12px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.5;
  }
  
  .portal-footer {
    background: #fff;
    border-top: 1px solid #ebeef5;
    text-align: center;
    padding: 20px;
    color: #909399;
    height: 60px;
    box-sizing: border-box;
    flex-shrink: 0;
  }
  </style>
  
  <script setup>
  import { ref } from 'vue';
  import { ElNotification } from 'element-plus';

  // 更新日志对话框状态
  const updateLogVisible = ref(false);

  // 显示更新日志
  const showUpdateLog = () => {
    updateLogVisible.value = true;
  };
  const apps = [
    {
      name: '通讯录',
      path: '/address_book.html',
      desc: '查询人员联系方式',
      color: 'linear-gradient(135deg, #0077dd 0%, #5c70ff 100%)',
      icon: '📞',
      category: 'public'
    },
        {
      name: '通讯录管理',
      path: '/address_book_management.html',
      desc: '管理人员联系方式',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      icon: '📋',
      category: 'private'
    },

    {
      name: '人员调动模拟',
      path: 'PerSimTS',
      desc: '模拟部门人员调动',
      color: 'linear-gradient(135deg, #f15312 0%, #ff8968 100%)',
      icon: '🔄',
      category: 'private'
    },
    {
      name: '警情质量监督',
      path: 'pcqss',
      desc: '警情质量监督',
      color: 'linear-gradient(135deg, #1154eb 0%, #5c70ff 100%)',
      icon: '🚨',
      category: 'private'

    },
    {
      name: '文件快递柜',
      path: 'fileCloud',
      desc: '文件上传与下载',
      color: 'linear-gradient(135deg, #fa541c 0%, #ff8968 100%)',
      icon: '📦',
      category: 'private'
    },
    {
      name: '学生信息查询系统',
      path: 'schInfo',
      desc: '学生信息查询',
      color: 'linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)',
      icon: '🎓',
      category: 'private'
    },
    {
      name: '数据大屏',
      path: '/back.html',
      desc: '岳池县110数据大屏预览',
      color: 'linear-gradient(135deg, #722ed1 0%, #9254de 100%)',
      icon: '📊',
      category: 'private'
    },
    {
      name: '值班管理',
      path: '/back.html',
      desc: '岳池县公安局内部值班管理',
      color: 'linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%)',
      icon: '🕐',
      category: 'private'
    },
    {
      name: '重点人员管理',
      path: '/back.html',
      desc: '重点人员基本信息，动向管理',
      color: 'linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)',
      icon: '👥',
      category: 'private'
    },
    {
      name: '重大事件管理',
      path: '/back.html',
      desc: '重点事件基本信息，处置情况管理',
      color: 'linear-gradient(135deg, #faad14 0%, #ffc53d 100%)',
      icon: '⚠️',
      category: 'private'
    },
    {
      name: '单位管理',
      path: '/back.html',
      desc: '',
      color: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)',
      icon: '🏢',
      category: 'system'
    },
    {
      name: '用户管理',
      path: '/back.html',
      desc: '',
      color: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
      icon: '👤',
      category: 'system'
    },
        {
      name: '授权管理',
      path: '/back.html',
      desc: '',
      color: 'linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)',
      icon: '🔐',
      category: 'system'
    },
    {
      name: '应用管理',
      path: '/back.html',
      desc: '',
      color: 'linear-gradient(135deg, #a0d911 0%, #b7eb8f 100%)',
      icon: '📱',
      category: 'system'
    },
        {
      name: '角色管理',
      path: '/back.html',
      desc: '',
      color: 'linear-gradient(135deg, #fa8c16 0%, #ffb347 100%)',
      icon: '🎭',
      category: 'system'
    }
  ];
  
  // 根据分类筛选应用
  const publicApps = apps.filter(app => app.category === 'public');
  const privateApps = apps.filter(app => app.category === 'private');
  const systemApps = apps.filter(app => app.category === 'system');
  
  const navigateTo = (path) => {
     // 根据路径判断跳转方式
     if (path === '/undefined' ) {
       ElNotification({
         title: '提示',
         message: '应用开发中，预计上线时间：2025年07月28日！',
         type: 'info',
         duration: 3000
       });
       return;
     }
     else{
       window.location.href = path;
     }

   
}
  </script>    